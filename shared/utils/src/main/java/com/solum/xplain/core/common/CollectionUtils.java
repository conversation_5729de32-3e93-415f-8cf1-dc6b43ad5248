package com.solum.xplain.core.common;

import static java.lang.Math.max;
import static java.util.function.Function.identity;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;
import static org.apache.commons.collections4.CollectionUtils.isEqualCollection;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.common.collect.Iterators;
import com.google.common.collect.Lists;
import io.atlassian.fugue.Checked;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.UnaryOperator;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CollectionUtils {

  public static boolean nullSafeIsEqualCollection(final Collection<?> a, final Collection<?> b) {
    return isEqualCollection(emptyIfNull(a), emptyIfNull(b));
  }

  @Nullable
  public static <T> T getLast(@Nullable final Collection<T> cc) {
    return Checked.now(() -> Iterables.getLast(emptyIfNull(cc))).toEither().rightOr(l -> null);
  }

  public static <A, B> List<B> convertCollectionTo(
      final Collection<A> collection, Function<A, B> mapFn) {
    return collection == null ? List.of() : collection.stream().map(mapFn).toList();
  }

  public static <K, V> Map<K, V> toMap(
      final Collection<V> collection, final Function<V, K> keyMapper) {
    return emptyIfNull(collection).stream().collect(Collectors.toMap(keyMapper, identity()));
  }

  public static <K, V> Map<K, V> toMap(
      final Collection<V> collection,
      final Function<V, K> keyMapper,
      BinaryOperator<V> mergeFunction) {
    return emptyIfNull(collection).stream()
        .collect(Collectors.toMap(keyMapper, identity(), mergeFunction));
  }

  public static <K, V> Map<K, V> parallelToMap(
      final Collection<V> collection, final Function<V, K> keyMapper) {
    return emptyIfNull(collection).parallelStream()
        .collect(Collectors.toMap(keyMapper, identity()));
  }

  public static <K, V> Map<K, V> toMapConcurrent(
      final Collection<V> collection, final Function<V, K> keyMapper) {
    return emptyIfNull(collection).parallelStream()
        .collect(Collectors.toConcurrentMap(keyMapper, identity()));
  }

  public static <K, V> Map<K, List<V>> parallelToGroupMap(
      final Collection<V> collection, final Function<V, K> keyMapper) {
    return emptyIfNull(collection).parallelStream().collect(Collectors.groupingBy(keyMapper));
  }

  public static <K, V> Map<K, List<V>> toGroupMap(
      final Collection<V> collection, final Function<V, K> keyMapper) {
    return emptyIfNull(collection).stream().collect(Collectors.groupingBy(keyMapper));
  }

  public static <K, V> Map<K, List<V>> toGroupMapConcurrent(
      final Collection<V> collection, final Function<V, K> keyMapper) {
    return emptyIfNull(collection).parallelStream()
        .collect(Collectors.groupingByConcurrent(keyMapper));
  }

  /**
   * To be used as a merge function in toMap() collectors when we want to discard duplicates.
   *
   * <p>This means if the stream has two entries that map to the same key, the first encountered
   * value will be taken with no regard to the values themselves.
   *
   * <p>Use with {@link #toMap(Collection, Function, BinaryOperator)} or {@link
   * Collectors#toMap(Function, Function, BinaryOperator)}.
   *
   * <p>See {@link
   * com.solum.xplain.xm.excmngmt.market.OverlayMarketDataSource#mergeIdenticalOrThrow} for a
   * similar operator that only ignores identical duplicate {@link java.math.BigDecimal} values.
   *
   * @return a binary operator that always returns the first argument
   * @param <T> the type of the elements being merged
   */
  public static <T> BinaryOperator<T> discardDuplicates() {
    return (a, b) -> a;
  }

  public static <T> Predicate<T> distinctByKeyFilter(Function<? super T, ?> keyExtractor) {
    Set<Object> seen = ConcurrentHashMap.newKeySet();
    return t -> seen.add(keyExtractor.apply(t));
  }

  @SafeVarargs
  public static <T> List<T> join(final Collection<T>... collections) {
    return Lists.newArrayList(Iterables.concat(collections));
  }

  public static boolean hasDuplicates(final Collection<?> collection) {
    if (collection == null) {
      return false;
    }
    return collection.size() != Set.copyOf(collection).size();
  }

  /**
   * Merge two sets into a single, immutable set.
   *
   * @param a the first set
   * @param b the second set
   * @return a new set containing all elements from both sets
   */
  public static <T> ImmutableSet<T> mergedSet(Set<T> a, Set<T> b) {
    return ImmutableSet.<T>builderWithExpectedSize(max(a.size(), b.size()))
        .addAll(a)
        .addAll(b)
        .build();
  }

  /**
   * Append an element to a list without modifying the original list. If the original list is null,
   * a new list with the element is created.
   *
   * @param original original list
   * @param element element to append
   * @return a new list with all elements from the original list and the new element appended
   * @param <T> list type
   */
  public static <T> List<T> copyListAndAppend(@Nullable List<T> original, T element) {
    List<T> shallowCopy;
    if (original == null) {
      shallowCopy = new ArrayList<>(1);
    } else {
      shallowCopy = new ArrayList<>(original.size() + 1);
      shallowCopy.addAll(original);
    }
    shallowCopy.add(element);
    return shallowCopy;
  }

  /**
   * Remove the last element from a list without modifying the original list. If the list is empty,
   * this method simply returns a copy of the list.
   *
   * @param original original list, must not be null
   * @return a new list with all elements from the original list and the new element appended
   * @param <T> list type
   */
  public static <T> List<T> copyListAndRemoveLast(@NonNull List<T> original) {
    List<T> shallowCopy = new ArrayList<>(original);
    if (!shallowCopy.isEmpty()) {
      shallowCopy.remove(original.size() - 1);
    }
    return shallowCopy;
  }

  /**
   * Modify the last element in the list without modifying the original list, which must not be
   * null. If the list is empty, this method simply returns a copy of the list.
   *
   * @param original original list, must not be null
   * @param operator the remapping function to compute the replacement last element
   * @return a new list with all elements from the original list and the last element replaced
   * @param <T> list type
   */
  public static <T> List<T> copyListAndReplaceLast(
      @NonNull List<T> original, @NonNull UnaryOperator<T> operator) {
    List<T> shallowCopy = new ArrayList<>(original);
    if (!shallowCopy.isEmpty()) {
      int lastIndex = shallowCopy.size() - 1;
      T oldValue = shallowCopy.get(lastIndex);
      T newValue = operator.apply(oldValue);
      shallowCopy.set(lastIndex, newValue);
    }
    return shallowCopy;
  }

  /**
   * Split a stream into chunks of 1000 so that we can batch downstream operations.
   *
   * <p>1000 is a reasonable choice for operations such as bulk MongoDB operations where the saving
   * is substantial but without risking having a document that is too large with what is likely to
   * be an array of ids.
   *
   * @param stream the stream to split
   * @return a stream of lists, each containing a chunk of the original stream
   */
  public static <T> Stream<List<T>> chunked(Stream<T> stream) {
    return chunked(stream, 1000);
  }

  /**
   * Split a stream into chunks of a given size so that we can batch downstream operations.
   *
   * @param stream the stream to split
   * @param chunkSize the size of each chunk
   * @return a stream of lists, each containing a chunk of the original stream
   */
  public static <T> Stream<List<T>> chunked(Stream<T> stream, int chunkSize) {
    return StreamSupport.stream(
        Spliterators.spliteratorUnknownSize(
            Iterators.partition(stream.iterator(), chunkSize), Spliterator.ORDERED),
        false);
  }

  /**
   * A list collector that takes a size parameter to preallocate the list.
   *
   * @param size initial size of the list
   */
  public static <T> Collector<T, ?, List<T>> toSizedList(int size) {
    return Collectors.toCollection(() -> new ArrayList<T>(size));
  }
}
