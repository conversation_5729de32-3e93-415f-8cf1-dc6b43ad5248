package com.solum.xplain.shared.spring.async;

import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@AutoConfiguration
public class SpringAsyncAutoConfiguration {

  @Configuration
  @ConditionalOnProperty(
      prefix = "app.virtual-threads-monitor",
      name = "enabled",
      havingValue = "true")
  @EnableConfigurationProperties({VirtualThreadsMonitorProperties.class})
  static class VirtualThreadsMonitorConfiguration {
    @Resource VirtualThreadsMonitorProperties properties;

    @Bean
    VirtualThreadsMonitor virtualThreadsMonitor(
        List<VirtualThreadPinnedEventHandler> eventHandlers) {
      return new VirtualThreadsMonitor(properties, eventHandlers);
    }

    @ConditionalOnProperty(
        prefix = "app.virtual-threads-monitor.metrics",
        name = "enabled",
        havingValue = "true")
    @ConditionalOnBean(MeterRegistry.class)
    static class VirtualThreadsMonitorMetricsConfiguration {
      @Bean
      VirtualThreadPinnedEventHandler virtualThreadPinnedMetricsHandler(
          VirtualThreadsMonitorProperties properties, MeterRegistry meterRegistry) {
        return new VirtualThreadPinnedEventHandler.Metrics(properties.metrics(), meterRegistry);
      }
    }

    @ConditionalOnProperty(
        prefix = "app.virtual-threads-monitor.logging",
        name = "enabled",
        havingValue = "true")
    @Bean
    VirtualThreadPinnedEventHandler virtualThreadPinnedLoggingHandler() {
      return new VirtualThreadPinnedEventHandler.Logging(properties.logging());
    }
  }
}
