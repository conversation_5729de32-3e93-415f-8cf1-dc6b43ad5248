package com.solum.xplain.xm.dashboards;

import static io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics.monitor;
import static java.util.concurrent.CompletableFuture.completedFuture;
import static java.util.function.Function.identity;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Weigher;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import com.solum.xplain.core.utils.XplainCaffeineCacheLoader;
import com.solum.xplain.shared.spring.async.AsyncUtils;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementResultMapper;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletionException;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.stereotype.Service;

/**
 * Service to obtain trades. When doing a bulk query the result is cached both in bulk and also the
 * individual trades are cached so that they can be retrieved individually from the cache without
 * further queries.
 *
 * <p>TODO(10608): Cache of cache is a smell.
 */
@Service
@Slf4j
@NullMarked
public class CachingDashboardTradesService implements CacheInvalidationListener {
  public static final Duration FRESHNESS_LIMIT = Duration.of(20, ChronoUnit.MINUTES);
  public static final long SIZE_LIMIT = 200_000L;
  private final PortfolioItemRepository portfolioItemRepository;
  private final IpvExceptionManagementResultMapper resultMapper;
  private final MeterRegistry meterRegistry;

  // TODO: Caffeine AsyncCache might need access to Spring classloader so maybe not the default FJP.
  private final AsyncLoadingCache<BitemporalDate, StateDateCache> dashboardTradesCache;

  public CachingDashboardTradesService(
      PortfolioItemRepository portfolioItemRepository,
      IpvExceptionManagementResultMapper resultMapper,
      MeterRegistry meterRegistry) {
    this.portfolioItemRepository = portfolioItemRepository;
    this.resultMapper = resultMapper;
    this.meterRegistry = meterRegistry;
    this.dashboardTradesCache =
        monitor(
            meterRegistry,
            AsyncUtils.newCaffeineBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .buildAsync(StateDateCache::new),
            "(VDXM) StateDateCache");
  }

  @RequiredArgsConstructor
  private class StateDateCache {
    private final BitemporalDate stateDate;

    private record PortfolioTradeEntityKey(String portfolioId, String portfolioItemEntityId) {}

    private final AsyncLoadingCache<String, List<Trade>> portfolioTradesCache =
        monitor(
            meterRegistry,
            AsyncUtils.newCaffeineBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .weigher((Weigher<String, List<Trade>>) (key, value) -> value.size())
                .maximumWeight(SIZE_LIMIT)
                .buildAsync(
                    XplainCaffeineCacheLoader.<String, List<Trade>>bulkCacheLoader(
                        this::singleLoad, this::bulkLoad)),
            "(VDXM) PortfolioTradesCache");

    private final AsyncLoadingCache<PortfolioTradeEntityKey, Trade> portfolioTradeCache =
        monitor(
            meterRegistry,
            AsyncUtils.newCaffeineBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .maximumSize(SIZE_LIMIT)
                .buildAsync(key -> loadTrade(key.portfolioId(), key.portfolioItemEntityId())),
            "(VDXM) PortfolioTradeCache)");

    public Stream<Trade> dashboardTradesStream(String portfolioId) {
      List<Trade> trades = portfolioTradesCache.get(portfolioId).join();
      return trades.stream();
    }

    public @Nullable Trade dashboardTrade(String portfolioId, String portfolioItemEntityId) {
      return portfolioTradeCache
          .get(new PortfolioTradeEntityKey(portfolioId, portfolioItemEntityId))
          .join();
    }

    private Trade loadTrade(String portfolioId, String portfolioItemEntityId) {
      log.debug(
          "Loading single trade {}/{} ({})",
          portfolioId,
          portfolioItemEntityId,
          stateDate.getActualDate());
      return portfolioItemRepository
          .portfolioItemLatest(portfolioId, portfolioItemEntityId, stateDate)
          .map(resultMapper::fromPortfolioItem)
          .getOrThrow(
              () -> new IllegalArgumentException("Trade not found: " + portfolioItemEntityId));
    }

    private List<Trade> singleLoad(String portfolioId) {
      log.debug(
          "Loading active trades for portfolio {} ({})", portfolioId, stateDate.getActualDate());
      return portfolioItemRepository
          .activePortfolioItemsStream(portfolioId, stateDate)
          .map(resultMapper::fromPortfolioItem)
          .peek(
              trade -> {
                // Prime the single-trade cache here
                portfolioTradeCache.put(
                    new PortfolioTradeEntityKey(trade.getPortfolioId(), trade.getEntityId()),
                    completedFuture(trade));
              })
          .toList();
    }

    private Map<String, List<Trade>> bulkLoad(Iterable<? extends String> keys) {
      var allKeys = new HashSet<String>();
      keys.forEach(allKeys::add);
      var allTradesByPortfolioId =
          StreamEx.of(portfolioItemRepository.activePortfolioItemsStream(allKeys, stateDate))
              .map(resultMapper::fromPortfolioItem)
              .peek(
                  trade -> {
                    // Prime the single-trade cache here
                    portfolioTradeCache.put(
                        new PortfolioTradeEntityKey(trade.getPortfolioId(), trade.getEntityId()),
                        completedFuture(trade));
                  })
              .groupingBy(Trade::getPortfolioId);
      return StreamEx.of(allKeys)
          .toMap(
              identity(),
              portfolioId -> allTradesByPortfolioId.getOrDefault(portfolioId, List.of()));
    }
  }

  public Stream<Trade> dashboardTradesStream(String portfolioId, BitemporalDate stateDate) {
    return dashboardTradesCache.get(stateDate).join().dashboardTradesStream(portfolioId);
  }

  public Trade dashboardTrade(
      String portfolioId, String portfolioItemEntityId, BitemporalDate stateDate) {
    try {
      return dashboardTradesCache
          .get(stateDate)
          .join()
          .dashboardTrade(portfolioId, portfolioItemEntityId);
    } catch (CompletionException e) {
      throw new IllegalArgumentException("Trade not found: " + portfolioItemEntityId, e);
    }
  }

  public void prefetchDashboardTrades(Set<String> portfolioIds, BitemporalDate stateDate) {
    // ignore the results, but preload the cache.
    dashboardTradesCache.get(stateDate).join().portfolioTradesCache.getAll(portfolioIds).join();
  }

  @Override
  public void invalidateAll() {
    this.dashboardTradesCache.synchronous().invalidateAll();
  }
}
