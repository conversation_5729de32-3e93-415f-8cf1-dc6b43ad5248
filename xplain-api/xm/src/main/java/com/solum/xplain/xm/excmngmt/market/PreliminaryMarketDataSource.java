package com.solum.xplain.xm.excmngmt.market;

import static com.solum.xplain.core.common.CollectionUtils.discardDuplicates;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

import com.solum.xplain.core.calculationapi.PreliminaryMarketDataProvider;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationMarketValueResolver;
import com.solum.xplain.core.curvemarket.InstrumentTypeDateMarketDataExtractionParams;
import com.solum.xplain.core.curvemarket.MarketDataExtractionParams;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketData;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValue;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMapperFunction;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.repository.MarketDataKeyRepository;
import com.solum.xplain.core.market.value.MarketDataKeyView;
import com.solum.xplain.core.market.value.MarketDataProviderTickerView;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketData;
import com.solum.xplain.xm.excmngmt.market.value.ResolvedInstrument;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import com.solum.xplain.xm.excmngmt.process.view.PreliminaryMarketDataView;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class PreliminaryMarketDataSource implements PreliminaryMarketDataProvider {

  private final CleanMarketDataRepository cleanMarketDataRepository;
  private final CurveConfigurationMarketValueResolver valueResolver;
  private final MarketDataKeyRepository keyRepository;

  @Override
  public Either<ErrorItem, CalculationMarketData> provide(MarketDataExtractionParams mdParams) {
    return valueResolver.curveConfigurationMarketData(mdParams, resolvedValues(mdParams));
  }

  @Override
  public <T extends CalculationMarketValue> Map<String, T> provideMarketData(
      MarketDataExtractionParams mdParams, CalculationMarketValueMapperFunction<T> toValue) {
    return valueResolver
        .resolveMarketData(
            mdParams.getStateDate(),
            mdParams.getConfigurationId(),
            mdParams.getPriceTypeResolver(),
            resolvedValues(mdParams),
            toValue)
        .stream()
        .collect(
            Collectors.toMap(
                CalculationMarketValue::getKey, Function.identity(), discardDuplicates()));
  }

  private Map<String, ResolvedMarketData> resolvedValues(MarketDataExtractionParams mdParams) {
    var keyMap =
        keyRepository.marketDataKeyViews(mdParams.getStateDate()).stream()
            .collect(
                toMap(
                    MarketDataKeyView::getKey,
                    v ->
                        Stream.ofNullable(v.getProviderTickers())
                            .flatMap(Collection::stream)
                            .collect(
                                toMap(MarketDataProviderTickerView::getCode, Function.identity())),
                    discardDuplicates()));

    return cleanPreliminaryData(mdParams).stream()
        .collect(
            Collectors.groupingBy(
                ResolvedInstrument::getInstrumentKey,
                mapping(ResolvedInstrument::resolvedData, toList())))
        .entrySet()
        .stream()
        .flatMap(v -> toResolvedView(v.getKey(), keyMap.get(v.getKey()), v.getValue()).stream())
        .collect(
            toMap(PreliminaryMarketDataView::getKey, Function.identity(), discardDuplicates()));
  }

  private List<ResolvedInstrument> cleanPreliminaryData(MarketDataExtractionParams mdParams) {
    if (mdParams
        instanceof InstrumentTypeDateMarketDataExtractionParams instrumentOverrideMdParams) {
      return cleanMarketDataRepository.cleanPreliminaryData(
          instrumentOverrideMdParams.getMarketDataGroupId(),
          instrumentOverrideMdParams.getCurveDateDetails());
    }
    return cleanMarketDataRepository.cleanPreliminaryData(
        mdParams.getMarketDataGroupId(), mdParams.getCurveDate());
  }

  private Optional<PreliminaryMarketDataView> toResolvedView(
      String key,
      Map<String, MarketDataProviderTickerView> mapping,
      List<ProviderData> providerData) {
    return Optional.ofNullable(mapping)
        .map(v -> PreliminaryMarketDataView.fromProviderData(key, providerData, v))
        .filter(v -> !v.getValues().isEmpty());
  }
}
