package com.solum.xplain.xm.excmngmt.market;

import static com.solum.xplain.core.common.CollectionUtils.discardDuplicates;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.calculationapi.OverlayMarketDataProvider;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationMarketValueResolver;
import com.solum.xplain.core.curvemarket.MarketDataExtractionParams;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketData;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValue;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMapperFunction;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.mdvalue.MarketDataValueService;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketData;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.xm.excmngmt.market.value.ResolvedInstrument;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.util.Map;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class OverlayMarketDataSource implements OverlayMarketDataProvider {

  private final CurveConfigurationMarketValueResolver resolver;
  private final MarketDataValueService marketDataValueService;
  private final CleanMarketDataRepository cleanMarketDataRepository;
  private static final BinaryOperator<BigDecimal> mergeIdenticalOrThrow =
      (a, b) -> {
        if (a.compareTo(b) == 0) {
          return a;
        }
        throw new IllegalStateException("Duplicate keys with different values: " + a + " and " + b);
      };

  @Override
  public Either<ErrorItem, CalculationMarketData> provide(MarketDataExtractionParams mdParams) {
    return resolvedValuesByKey(mdParams)
        .flatMap(d -> resolver.curveConfigurationMarketData(mdParams, d))
        .map(v -> v.applyOverlay(cleanValuesMap(mdParams)));
  }

  @Override
  public <T extends CalculationMarketValue> Map<String, T> provideMarketData(
      MarketDataExtractionParams mdParams, CalculationMarketValueMapperFunction<T> toValue) {
    var cleanValuesMap = cleanValuesMap(mdParams);
    return resolver
        .resolveMarketData(
            mdParams.getStateDate(),
            mdParams.getConfigurationId(),
            mdParams.getPriceTypeResolver(),
            resolvedValuesByKey(mdParams).getOrElse(Map.of()),
            toValue)
        .stream()
        .flatMap(
            v ->
                ofNullable(cleanValuesMap.get(v.getKey())).stream()
                    .map(
                        val -> {
                          v.withOverlayValue(val);
                          return v;
                        }))
        .collect(
            Collectors.toMap(
                CalculationMarketValue::getKey, Function.identity(), discardDuplicates()));
  }

  private Map<String, Map<ValueBidAskType, BigDecimal>> cleanValuesMap(
      MarketDataExtractionParams mdParams) {
    return cleanMarketDataRepository
        .cleanOverlayData(
            mdParams.getMarketDataGroupId(), mdParams.getCurveDate(), mdParams.getConfigurationId())
        .stream()
        .collect(
            Collectors.groupingBy(
                ResolvedInstrument::getInstrumentKey,
                Collectors.toMap(
                    ResolvedInstrument::bidAskType,
                    ResolvedInstrument::resolvedValue,
                    mergeIdenticalOrThrow)));
  }

  private Either<ErrorItem, Map<String, ResolvedMarketData>> resolvedValuesByKey(
      MarketDataExtractionParams mdParams) {
    return mdParams.resolvedValuesByKey(marketDataValueService);
  }
}
