package com.solum.xplain.xm.excmngmt.processipv;

import static com.solum.xplain.core.common.CollectionUtils.discardDuplicates;
import static io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics.monitor;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.shared.spring.async.AsyncUtils;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import com.solum.xplain.xm.dashboards.views.DashboardDateView;
import com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.value.EntryBreakHistory;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.stereotype.Service;

/** Service to obtain previous breaks for individual trades, by querying and caching it in bulk. */
@Service
@Slf4j
@NullMarked
public class CachingBreakTestHistoryService implements CacheInvalidationListener {
  public static final Duration FRESHNESS_LIMIT = Duration.of(5, ChronoUnit.MINUTES);
  private final IpvExceptionManagementDataService ipvExceptionManagementDataService;
  private final IpvExceptionManagementCalculationRepository
      ipvExceptionManagementCalculationRepository;
  private final MeterRegistry meterRegistry;
  private final AsyncLoadingCache<BitemporalDate, StateDateCache> providerDataCache;

  public CachingBreakTestHistoryService(
      IpvExceptionManagementDataService ipvExceptionManagementDataService,
      IpvExceptionManagementCalculationRepository ipvExceptionManagementCalculationRepository,
      MeterRegistry meterRegistry) {
    this.ipvExceptionManagementDataService = ipvExceptionManagementDataService;
    this.ipvExceptionManagementCalculationRepository = ipvExceptionManagementCalculationRepository;
    this.meterRegistry = meterRegistry;
    this.providerDataCache =
        monitor(
            meterRegistry,
            AsyncUtils.newCaffeineBuilder()
                .expireAfterAccess(FRESHNESS_LIMIT)
                .buildAsync(StateDateCache::new),
            "(VDXM) BreakTestHistoryCache");
  }

  /** Internal cache for a single bitemporal date. */
  @RequiredArgsConstructor
  private class StateDateCache {
    private final BitemporalDate stateDate;

    private record PreviousDayKey(String portfolioId, IpvExceptionManagementPhase phase) {}

    AsyncLoadingCache<PreviousDayKey, Map<String, EntryBreakHistory>> previousDayCache =
        monitor(
            meterRegistry,
            AsyncUtils.newCaffeineBuilder()
                .expireAfterWrite(FRESHNESS_LIMIT)
                .buildAsync(pdk -> previousDashboardBreaks(pdk.portfolioId(), pdk.phase())),
            "(VDXM) BreakTestHistoryCache->PreviousDayCache)");

    @Nullable
    public EntryBreakHistory breakHistory(Trade trade, IpvExceptionManagementPhase phase) {
      PreviousDayKey pdk = new PreviousDayKey(trade.getPortfolioId(), phase);
      Map<String, EntryBreakHistory> previousDayBreaks = previousDayCache.get(pdk).join();
      return previousDayBreaks.get(trade.getKey());
    }

    private Map<String, EntryBreakHistory> previousDashboardBreaks(
        String portfolioId, IpvExceptionManagementPhase phase) {
      log.debug(
          "Loading historic breaking test data for {}/{} ({})",
          portfolioId,
          phase,
          stateDate.getActualDate());

      Optional<DashboardDateView> previousDashboard =
          ipvExceptionManagementDataService.previousDashboard(
              portfolioId, stateDate.getActualDate());

      // If there are duplicate values for the same VDK in the previous dashboard results then
      // we discard the duplicates and keep one of them with no regard to the values. This deals
      // with the case where somehow the previous dashboard workflow has written results twice for
      // the same trade, by forking on two execution paths post-submit of the approval step. This is
      // the same as we already do for preliminary and overlay data in {@link
      // com.solum.xplain.xm.excmngmt.market.PreliminaryMarketDataSource} and {@link
      // com.solum.xplain.xm.excmngmt.market.OverlayMarketDataSource}, and avoids unnecessary
      // downstream effects whereby a duplicate result on one day would cause all subsequent
      // dashboards to fail to complete.
      return previousDashboard
          .map(
              d ->
                  ipvExceptionManagementCalculationRepository.portfolioDashboardBreaksHistory(
                      d.id(), phase, portfolioId))
          .map(
              breaks ->
                  CollectionUtils.toMap(
                      breaks, EntryBreakHistory::getUniqueKey, discardDuplicates()))
          .orElse(Collections.emptyMap());
    }
  }

  /**
   * Check caches for previous dashboard breaks for the VDG/portfolio/phase and fetch if missing,
   * then get the data for the specific trade from those.
   *
   * @param trade the trade to retrieve valuation data for
   * @param phase the exception management phase to retrieve data for
   * @param stateDate relevant state date
   * @return historical breaks from the previous dashboard for the trade
   */
  @Nullable
  public EntryBreakHistory previousDashboardBreaks(
      Trade trade, IpvExceptionManagementPhase phase, BitemporalDate stateDate) {
    return providerDataCache.get(stateDate).join().breakHistory(trade, phase);
  }

  @Override
  public void invalidateAll() {
    this.providerDataCache.synchronous().invalidateAll();
  }
}
