package com.solum.xplain.xm.dashboards

import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND

import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementResultMapper
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import io.atlassian.fugue.Either
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.util.stream.Stream
import spock.lang.Specification

class CachingDashboardTradesServiceTest extends Specification {
  def portfolioItemRepository = Mock(PortfolioItemRepository)
  def ipvExceptionManagementResultMapper = Mock(IpvExceptionManagementResultMapper)
  def stateDate = BitemporalDate.newOf(LocalDate.now())
  def service = new CachingDashboardTradesService(portfolioItemRepository, ipvExceptionManagementResultMapper, new SimpleMeterRegistry())

  def "service requests all trades for stateDate and resolves individual items from cache (preload)"() {
    given:
    def portfolioId = "p1"
    def tradeEntityId = "trade1"
    def portfolioItem = Mock(PortfolioItem)
    def trade = Mock(Trade, {
      getEntityId() >> tradeEntityId
      getPortfolioId() >> portfolioId
    })
    ipvExceptionManagementResultMapper.fromPortfolioItem(portfolioItem) >> trade

    def portfolioId2 = "p2"
    def tradeEntityId2 = "trade2"
    def portfolioItem2 = Mock(PortfolioItem)
    def trade2 = Mock(Trade, {
      getEntityId() >> tradeEntityId2
      getPortfolioId() >> portfolioId2
    })
    ipvExceptionManagementResultMapper.fromPortfolioItem(portfolioItem2) >> trade2

    when:
    service.prefetchDashboardTrades([portfolioId, portfolioId2] as Set, stateDate)
    def results = service.dashboardTradesStream(portfolioId, stateDate).toList()
    def result = service.dashboardTrade(portfolioId, tradeEntityId, stateDate)
    def results2 = service.dashboardTradesStream(portfolioId2, stateDate).toList()
    def result2 = service.dashboardTrade(portfolioId2, tradeEntityId2, stateDate)

    then:
    1 * portfolioItemRepository.activePortfolioItemsStream([portfolioId, portfolioId2] as Set, stateDate) >> Stream.of(portfolioItem, portfolioItem2)
    0 * portfolioItemRepository.portfolioItemLatest(_, _, _)
    results == [trade]
    result == trade
    results2 == [trade2]
    result2 == trade2
  }

  def "service requests all trades for stateDate and resolves individual items from cache (preload) with empty portfolios"() {
    given:
    def portfolioId = "p1"
    def portfolioId2 = "p2"
    def tradeEntityId = "trade1"
    def tradeEntityId2 = "trade2"
    def portfolioItem = Mock(PortfolioItem)
    def trade = Mock(Trade, {
      getEntityId() >> tradeEntityId
      getPortfolioId() >> portfolioId
    })
    ipvExceptionManagementResultMapper.fromPortfolioItem(portfolioItem) >> trade

    when:
    service.prefetchDashboardTrades([portfolioId, portfolioId2] as Set, stateDate)
    def results = service.dashboardTradesStream(portfolioId, stateDate).toList()
    def result = service.dashboardTrade(portfolioId, tradeEntityId, stateDate)
    def results2 = service.dashboardTradesStream(portfolioId2, stateDate).toList()

    then:
    1 * portfolioItemRepository.activePortfolioItemsStream([portfolioId, portfolioId2] as Set, stateDate) >> Stream.of(portfolioItem)
    0 * portfolioItemRepository.portfolioItemLatest(_, _, _)
    results == [trade]
    result == trade
    results2 == []

    when:
    def result2 = service.dashboardTrade(portfolioId2, tradeEntityId2, stateDate)

    then:
    1 * portfolioItemRepository.portfolioItemLatest(portfolioId2, tradeEntityId2, stateDate) >> Either.left(OBJECT_NOT_FOUND.entity("Trade not found"))
    thrown(IllegalArgumentException)
  }

  def "service requests all trades for stateDate and resolves individual items from cache"() {
    given:
    def portfolioId = "p1"
    def tradeEntityId = "trade1"
    def portfolioItem = Mock(PortfolioItem)
    def trade = Mock(Trade, {
      getEntityId() >> tradeEntityId
      getPortfolioId() >> portfolioId
    })
    ipvExceptionManagementResultMapper.fromPortfolioItem(portfolioItem) >> trade

    when:
    def results = service.dashboardTradesStream(portfolioId, stateDate).toList()
    def result = service.dashboardTrade(portfolioId, tradeEntityId, stateDate)

    then:
    1 * portfolioItemRepository.activePortfolioItemsStream(portfolioId, stateDate) >> Stream.of(portfolioItem)
    0 * portfolioItemRepository.portfolioItemLatest(_, _, _)
    results == [trade]
    result == trade
  }

  def "individual items fetched if cache not primed by bulk fetch"() {
    given:
    def portfolioId = "p1"
    def tradeEntityId = "trade1"
    def portfolioItem = Mock(PortfolioItem)
    def trade = Mock(Trade, {
      getEntityId() >> tradeEntityId
    })
    ipvExceptionManagementResultMapper.fromPortfolioItem(portfolioItem) >> trade

    when:
    def result = service.dashboardTrade(portfolioId, tradeEntityId, stateDate)

    then:
    0 * portfolioItemRepository.activePortfolioItemsStream(_, _)
    1 * portfolioItemRepository.portfolioItemLatest(portfolioId, tradeEntityId, stateDate) >> Either.right(portfolioItem)
    result == trade
  }

  def "individual item not found"() {
    given:
    def portfolioId = "p1"
    def tradeEntityId = "trade1"

    when:
    def result = service.dashboardTrade(portfolioId, tradeEntityId, stateDate)

    then:
    0 * portfolioItemRepository.activePortfolioItemsStream(_, _)
    1 * portfolioItemRepository.portfolioItemLatest(portfolioId, tradeEntityId, stateDate) >> Either.left(OBJECT_NOT_FOUND.entity("Trade not found"))
    thrown(IllegalArgumentException)
  }
}
