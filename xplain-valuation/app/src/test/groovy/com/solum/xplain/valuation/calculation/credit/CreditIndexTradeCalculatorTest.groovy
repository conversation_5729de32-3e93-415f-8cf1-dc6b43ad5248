package com.solum.xplain.valuation.calculation.credit

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.valuation.calculation.TradeSamples.creditIndexTrade
import static com.solum.xplain.valuation.calculation.TradeSamples.singlePeriodCreditIndexTrade
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.CurrencyAmount
import com.opengamma.strata.collect.array.DoubleArray
import com.opengamma.strata.product.common.BuySell
import com.solum.xplain.valuation.calculation.CalibrationRatesSample
import com.solum.xplain.valuation.calculation.MarketDataSample
import com.solum.xplain.valuation.calculation.ValuationOptions
import java.time.LocalDate
import spock.lang.Specification

class CreditIndexTradeCalculatorTest extends Specification {

  static def IMM_DT = LocalDate.parse("2024-03-20")
  static def START_DATE = LocalDate.parse("2023-12-20")
  static def END_DATE = LocalDate.parse("2024-06-20")

  def "should calculate CreditIndex metrics"() {
    setup:
    def rates = CalibrationRatesSample.valuationRates()
    def calculator = new CreditIndexTradeCalculator(
    creditIndexTrade(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, USD, true, "CDX_NA_HY_S1_V1"),
    () -> CreditRatesSample.creditCalibrationCurves(),
    rates.ratesProvider,
    CreditRatesSample.creditIndexRatesProvider(CreditRatesSample.CREDIT_INDEX_STANDARD_ID),
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    metrics != null
    metrics.presentValue == -3992.6546546561194
    metrics.accrued == -34.4125
    metrics.accruedLocalCcy == -27.77777777777778
    metrics.cleanPresentValue == -3958.2421546561195
    that metrics.cleanPresentValueLocalCcy, closeTo(CurrencyAmount.of(USD, -3958.2421546561195).convertedTo(EUR, rates.ratesProvider).getAmount(), 0.1)
    metrics.dv01 == 1.0982297615362426
    metrics.dv01LocalCcy == 0.8864913117296223
    that metrics.cs01, closeTo(1.1110041674401225, 0.00000000000001)
    that metrics.cs01LocalCcy, closeTo(0.8968028150624551, 0.00000000000001)
    that metrics.infcsbr01, closeTo(1.1110041674401225, 0.00000000000001)
    that metrics.infcsbr01LocalCcy, closeTo(0.8968028150624551, 0.00000000000001)
    metrics.payLegPV == -7123.878174809246
    metrics.receiveLegPV == 3891.007001813337
    metrics.payLegAccrued == -27.77777777777778
    metrics.dv01TradeValues.size() == 1
    metrics.cs01TradeValues.size() == 1
    that metrics.pv01, closeTo(0.39313, 0.1)
    that metrics.pv01LocalCcy, closeTo(0.3173, 0.1)
    metrics.spot01TradeValues.isEmpty()
  }

  def "calculate Credit Index metrics on dates near coupon dates"() {
    setup:
    def rates = CalibrationRatesSample.valuationRates(vd)
    def calculator =  new CreditIndexTradeCalculator(
    singlePeriodCreditIndexTrade(START_DATE, END_DATE, BuySell.SELL),
    ValuationOptions.newOf(EUR, USD, true, "CDX_NA_HY_S1_V1"),
    () -> CreditRatesSample.creditCalibrationCurves(),
    rates.ratesProvider,
    CreditRatesSample.creditIndexRatesProvider(CreditRatesSample.CREDIT_INDEX_STANDARD_ID, vd, DoubleArray.of(1e-6, 1e-6)),
    )

    def metrics = calculator.calculate(ReferenceData.standard())

    expect:
    metrics.localCcy == EUR.getCode()
    metrics.presentValuePayLegCurrency == pv
    metrics.cleanPresentValueLocalCcy == cleanPv
    metrics.accruedLocalCcy == accruedLocalCcy
    metrics.accrued == accrued
    if (parRate != null) {
      metrics.breakevenMetrics.parRate == parRate
    }
    metrics.pv01 == pv01

    where:
    vd                      | pv                  | cleanPv             | accrued             | accruedLocalCcy     | parRate               | pv01
    START_DATE.minusDays(2) | 25.**************   | 25.**************   | 0.0                 | 0.0                 | 9.91279844634114E-7   | -0.3153970267096032
    START_DATE.minusDays(1) | 25.***************  | 25.***************  | 0.0                 | 0.0                 | 9.912798446341089E-7  | -0.3154057887359322
    START_DATE              | 25.**************   | 25.3167493313257    | 0.1720625           | 0.1388888888888889  | 9.858787483292283E-7  | -0.31544959900922903
    START_DATE.plusDays(1)  | 25.456359151231293  | 25.178581373453515  | 0.344125            | 0.2777777777777778  | 9.80477802082381E-7   | -0.3154583624865377
    IMM_DT.minusDays(2)     | 25.519878062090093  | 13.019878062090093  | 15.485625           | 12.5                | 5.057816415070711E-7  | -0.31619534913437364
    IMM_DT.minusDays(1)     | 25.520600743413183  | 12.881711854524294  | 15.6576875          | 12.63888888888889   | 5.003940327652324E-7  | -0.31620413290390587
    IMM_DT                  | 12.882434555538829  | 12.74354566664994   | 0.1720625           | 0.1388888888888889  | 9.80607300512538E-7   | -0.15963186148649242
    IMM_DT.plusDays(1)      | 12.88280619112083   | 12.605028413343051  | 0.344125            | 0.2777777777777778  | 9.699350470962422E-7  | -0.15963629620036035
    END_DATE.minusDays(2)   | 12.915921594381695  | 0.2770327054928057  | 15.6576875          | 12.63888888888889   | 2.1290913930943946E-8 | -0.160022570004832
    END_DATE.minusDays(1)   | 12.916294125542452  | 0.13851634776467492 | 15.829749999999999  | 12.777777777777777  | 1.0645309126382146E-8 | -0.16003590577944293
    END_DATE                | 0.0                 | 0.0                 | 0.0                 | 0.0                 | null                  | 0.0
    END_DATE.plusDays(1)    | 0.0                 | 0.0                 | 0.0                 | 0.0                 | null                  | 0.0
  }

  def "calculate Credit Index metrics with shifted rates provider"() {
    setup:
    def rates = CalibrationRatesSample.valuationRates()
    def calculator = new CreditIndexTradeCalculator(
    creditIndexTrade(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, USD, true, "CDX_NA_HY_S1_V1"),
    () -> CreditRatesSample.creditCalibrationCurves(),
    rates.ratesProvider,
    CreditRatesSample.creditIndexShiftedRatesProvider(CreditRatesSample.CREDIT_INDEX_STANDARD_ID),
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    metrics.presentValue == -3992.6546546561194
    metrics.cleanPresentValue == -3958.2421546561195
    metrics.accrued == -34.4125
    metrics.accruedLocalCcy == -27.77777777777778
    metrics.spot01TradeValues.size() == 1
    with(metrics.spot01TradeValues.get(0)) {
      currencyPair() == "EUR/USD"
      that spot01(), closeTo(0, 0.0001)
      that spot01TradeCcy(), closeTo(0, 0.0001)
      fxSpot() == 1.23885
    }
  }
}
