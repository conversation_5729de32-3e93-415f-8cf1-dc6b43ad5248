package com.solum.xplain.valuation.executor;

import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.messages.trade.constant.ValuationProductType;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class TradeValuationExecutorResolverFactory {

  private static final String CALCULATOR_NOT_FOUND = "Calculator for %s not found";

  private final Map<TradeCalculatorType, TradeValuationExecutorResolver> resolverMap;

  public TradeValuationExecutorResolverFactory(List<TradeValuationExecutorResolver> resolver) {
    this.resolverMap =
        resolver.stream()
            .collect(
                Collectors.toMap(
                    TradeValuationExecutorResolver::calculatorType, Function.identity()));
  }

  public Either<ValuationError, TradeValuationExecutorResolver> getResolver(
      ValuationProductType type) {
    var calculatorType = TradeCalculatorType.fromProductType(type);
    var resolver = resolverMap.get(calculatorType);
    return resolver == null
        ? Either.left(new ValuationError(String.format(CALCULATOR_NOT_FOUND, type)))
        : Either.right(resolver);
  }
}
