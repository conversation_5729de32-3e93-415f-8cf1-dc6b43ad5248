package com.solum.xplain.valuation.executor;

import static com.solum.xplain.valuation.calculation.TradeCalculationUtils.options;

import com.solum.xplain.valuation.calculation.credit.CreditIndexTradeCalculator;
import com.solum.xplain.valuation.calibration.CalibrationCacheService;
import com.solum.xplain.valuation.common.ValuationError;
import com.solum.xplain.valuation.mapper.CreditIndexTradeMapper;
import com.solum.xplain.valuation.messages.trade.ValuationRequest;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import org.springframework.stereotype.Component;

@Component
public class CreditIndexValuationExecutorResolver implements TradeValuationExecutorResolver {

  private final CalibrationCacheService cacheService;
  private final CreditIndexTradeMapper tradeMapper;

  public CreditIndexValuationExecutorResolver(
      CalibrationCacheService cacheService, CreditIndexTradeMapper tradeMapper) {
    this.cacheService = cacheService;
    this.tradeMapper = tradeMapper;
  }

  @Override
  public TradeCalculatorType calculatorType() {
    return TradeCalculatorType.CREDIT_INDEX;
  }

  @Override
  public Either<ValuationError, TradeValuationExecutor> executor(ValuationRequest request) {
    return Steps.begin(cacheService.calibrationRates(request))
        .then(() -> cacheService.creditRates(request))
        .yield(
            (rates, creditRates) ->
                new CreditIndexTradeCalculator(
                    tradeMapper.toStrataTrade(request),
                    options(request),
                    cacheService.curvesSupplier(request),
                    rates.getRatesProvider(),
                    creditRates))
        .map(TradeValuationExecutor::newOf);
  }
}
